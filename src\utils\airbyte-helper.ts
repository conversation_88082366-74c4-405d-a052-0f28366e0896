export interface RawStreamMetadata {
   streamName: string;
   defaultCursorField: string[];
   sourceDefinedCursorField: boolean;
   sourceDefinedPrimaryKey: string[][];
   propertyFields: string[][];
}

export interface AirbyteCatalog {
   streams: AirbyteStream[];
}

export interface AirbyteStream {
   name: string;
   supported_sync_modes?: SyncMode[];
   json_schema: any;
   source_defined_cursor?: boolean;
   default_cursor_field?: string[];
   source_defined_primary_key?: string[][];
}

export interface ConfiguredAirbyteCatalog {
   streams: ConfiguredAirbyteStream[];
}

export interface ConfiguredAirbyteStream {
   stream: AirbyteStream;
   config: {
      sync_mode: SyncMode;
      destination_sync_mode: DestinationSyncMode;
      cursor_field: string[];
      primary_key: string[][];
   };
}

export type SyncMode = 'full_refresh' | 'incremental';
export type DestinationSyncMode = 'append' | 'overwrite' | 'append_dedup';

interface StreamSyncConfig {
   syncMode: 'incremental' | 'full_refresh';
   destinationSyncMode: 'append' | 'append_dedup' | 'overwrite';
   cursorField?: string[];
   primaryKey?: string[][];
}

export function getCustomStreamConfig(): Record<string, any> {
   // Define sync mode configurations
   const incrementalAppend = {
      sync_mode: 'incremental',
      destination_sync_mode: 'append',
   };

   const incrementalAppendDeduped = {
      sync_mode: 'incremental',
      destination_sync_mode: 'append_dedup',
   };

   const fullRefreshOverwrite = {
      sync_mode: 'full_refresh',
      destination_sync_mode: 'overwrite',
   };

   // Base stream configurations without account prefix
   const baseConfigs: Record<string, any> = {
      activities: incrementalAppend,
      ad_account: fullRefreshOverwrite,
      ad_creatives: fullRefreshOverwrite,
      ad_sets: incrementalAppendDeduped,
      ads: incrementalAppendDeduped,
      ads_insights: incrementalAppendDeduped,
      ads_insights_action_carousel_card: incrementalAppendDeduped,
      ads_insights_action_conversion_device: incrementalAppendDeduped,
      ads_insights_action_product_id: incrementalAppendDeduped,
      ads_insights_action_reaction: incrementalAppendDeduped,
      ads_insights_action_video_sound: incrementalAppendDeduped,
      ads_insights_action_video_type: incrementalAppendDeduped,
      ads_insights_action_type: incrementalAppendDeduped,
      ads_insights_age_and_gender: incrementalAppendDeduped,
      ads_insights_country: incrementalAppendDeduped,
      ads_insights_delivery_device: incrementalAppendDeduped,
      ads_insights_delivery_platform: incrementalAppendDeduped,
      ads_insights_delivery_platform_and_device_platform:
         incrementalAppendDeduped,
      ads_insights_demographics_age: incrementalAppendDeduped,
      ads_insights_demographics_country: incrementalAppendDeduped,
      ads_insights_demographics_dma_region: incrementalAppendDeduped,
      ads_insights_demographics_gender: incrementalAppendDeduped,
      ads_insights_dma: incrementalAppendDeduped,
      ads_insights_platform_and_device: incrementalAppendDeduped,
      ads_insights_region: incrementalAppendDeduped,
      campaigns: incrementalAppendDeduped,
      custom_audiences: fullRefreshOverwrite,
      custom_conversions: fullRefreshOverwrite,
      images: incrementalAppendDeduped,
      videos: incrementalAppendDeduped,
   };

   return baseConfigs;
}

// Helper function to get stream config by matching stream name patterns
export function getStreamConfig(streamName: string): {
   sync_mode: SyncMode;
   destination_sync_mode: DestinationSyncMode;
   cursor_field?: string[];
   primary_key?: string[][];
} | null {
   const baseConfigs = getCustomStreamConfig();

   // First try exact match
   if (baseConfigs[streamName]) {
      return baseConfigs[streamName];
   }

   // Try to match by removing account prefix (e.g., "a01029_activities" -> "activities")
   const streamNameWithoutPrefix = streamName.replace(/^[a-zA-Z0-9]+_/, '');
   if (baseConfigs[streamNameWithoutPrefix]) {
      return baseConfigs[streamNameWithoutPrefix];
   }

   return null;
}
