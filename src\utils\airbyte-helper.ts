export interface RawStreamMetadata {
   streamName: string;
   defaultCursorField: string[];
   sourceDefinedCursorField: boolean;
   sourceDefinedPrimaryKey: string[][];
   propertyFields: string[][];
}

export interface AirbyteCatalog {
   streams: AirbyteStream[];
}

export interface AirbyteStream {
   name: string;
   supported_sync_modes?: SyncMode[];
   json_schema: any;
   source_defined_cursor?: boolean;
   default_cursor_field?: string[];
   source_defined_primary_key?: string[][];
}

export interface ConfiguredAirbyteCatalog {
   streams: ConfiguredAirbyteStream[];
}

export interface ConfiguredAirbyteStream {
   stream: AirbyteStream;
   config: {
      sync_mode: SyncMode;
      destination_sync_mode: DestinationSyncMode;
      cursor_field: string[];
      primary_key: string[][];
   };
}

export type SyncMode = 'full_refresh' | 'incremental';
export type DestinationSyncMode = 'append' | 'overwrite' | 'append_dedup';

interface StreamSyncConfig {
   syncMode: 'incremental' | 'full_refresh';
   destinationSyncMode: 'append' | 'append_dedup' | 'overwrite';
   cursorField?: string[];
   primaryKey?: string[][];
}

export function getCustomStreamConfig(): Record<string, any> {
   const incrementalAppend = {
      sync_mode: 'incremental',
      destination_sync_mode: 'append',
   };

   const incrementalDeduped = {
      sync_mode: 'incremental',
      destination_sync_mode: 'append_dedup',
      cursor_field: ['updated_time'],
      primary_key: [['ad_id']],
   };

   const fullRefresh = {
      sync_mode: 'full_refresh',
      destination_sync_mode: 'overwrite',
   };

   return {
      activities: incrementalAppend,
      ad_account: fullRefresh,
      ad_creatives: fullRefresh,
      ad_sets: incrementalDeduped,
      ads: incrementalDeduped,
      ads_insights: incrementalDeduped,
      ads_insights_action_type: incrementalDeduped,
      ads_insights_age_and_gender: incrementalDeduped,
      ads_insights_country: incrementalDeduped,
      ads_insights_platform_and_device: incrementalDeduped,
      ads_insights_region: incrementalDeduped,
      campaigns: incrementalDeduped,
      custom_audiences: fullRefresh,
      custom_conversions: fullRefresh,
      images: incrementalDeduped,
      videos: incrementalDeduped,
   };
}
