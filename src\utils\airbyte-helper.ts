export interface RawStreamMetadata {
   streamName: string;
   defaultCursorField: string[];
   sourceDefinedCursorField: boolean;
   sourceDefinedPrimaryKey: string[][];
   propertyFields: string[][];
}

export interface AirbyteCatalog {
   streams: AirbyteStream[];
}

export interface AirbyteStream {
   name: string;
   supported_sync_modes?: SyncMode[];
   json_schema: any;
   source_defined_cursor?: boolean;
   default_cursor_field?: string[];
   source_defined_primary_key?: string[][];
}

export interface ConfiguredAirbyteCatalog {
   streams: ConfiguredAirbyteStream[];
}

export interface ConfiguredAirbyteStream {
   stream: AirbyteStream;
   config: {
      sync_mode: SyncMode;
      destination_sync_mode: DestinationSyncMode;
      cursor_field: string[];
      primary_key: string[][];
   };
}

export type SyncMode = 'full_refresh' | 'incremental';
export type DestinationSyncMode = 'append' | 'overwrite' | 'append_dedup';

interface StreamSyncConfig {
   syncMode: 'incremental' | 'full_refresh';
   destinationSyncMode: 'append' | 'append_dedup' | 'overwrite';
   cursorField?: string[];
   primaryKey?: string[][];
}

interface StreamConfig {
   sync_mode: SyncMode;
   destination_sync_mode: DestinationSyncMode;
   cursor_field: string[];
   primary_key: string[][];
}

export function getCustomStreamConfig(): Record<string, StreamConfig> {
   // Define sync mode configurations with proper cursor_field and primary_key
   const incrementalAppend: StreamConfig = {
      sync_mode: 'incremental',
      destination_sync_mode: 'append',
      cursor_field: [],
      primary_key: [],
   };

   const incrementalAppendDeduped: StreamConfig = {
      sync_mode: 'incremental',
      destination_sync_mode: 'append_dedup',
      cursor_field: [],
      primary_key: [],
   };

   const fullRefreshOverwrite: StreamConfig = {
      sync_mode: 'full_refresh',
      destination_sync_mode: 'overwrite',
      cursor_field: [],
      primary_key: [],
   };

   // ONLY the streams you specifically requested
   const baseConfigs: Record<string, StreamConfig> = {
      activities: incrementalAppend,
      ad_account: fullRefreshOverwrite,
      ad_creatives: fullRefreshOverwrite,
      ad_sets: incrementalAppendDeduped,
      ads: incrementalAppendDeduped,
      ads_insights: incrementalAppendDeduped,
      ads_insights_action_type: incrementalAppendDeduped,
      ads_insights_age_and_gender: incrementalAppendDeduped,
      ads_insights_country: incrementalAppendDeduped,
      ads_insights_platform_and_device: incrementalAppendDeduped,
      ads_insights_region: incrementalAppendDeduped,
      campaigns: incrementalAppendDeduped,
      custom_audiences: fullRefreshOverwrite,
      custom_conversions: fullRefreshOverwrite,
      images: incrementalAppendDeduped,
   };

   return baseConfigs;
}

// Helper function to get stream config by matching stream name patterns
export function getStreamConfig(streamName: string): StreamConfig | null {
   const baseConfigs = getCustomStreamConfig();

   // First try exact match
   if (baseConfigs[streamName]) {
      return baseConfigs[streamName];
   }

   // Try to match by removing account prefix (e.g., "a01029_activities" -> "activities")
   const streamNameWithoutPrefix = streamName.replace(/^[a-zA-Z0-9]+_/, '');
   if (baseConfigs[streamNameWithoutPrefix]) {
      return baseConfigs[streamNameWithoutPrefix];
   }

   return null;
}
