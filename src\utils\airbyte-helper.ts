export interface RawStreamMetadata {
   streamName: string;
   defaultCursorField: string[];
   sourceDefinedCursorField: boolean;
   sourceDefinedPrimaryKey: string[][];
   propertyFields: string[][];
}

export interface AirbyteCatalog {
   streams: AirbyteStream[];
}

export interface AirbyteStream {
   name: string;
   supported_sync_modes?: SyncMode[];
   json_schema: any;
   source_defined_cursor?: boolean;
   default_cursor_field?: string[];
   source_defined_primary_key?: string[][];
}

export interface ConfiguredAirbyteCatalog {
   streams: ConfiguredAirbyteStream[];
}

export interface ConfiguredAirbyteStream {
   stream: AirbyteStream;
   config: {
      sync_mode: SyncMode;
      destination_sync_mode: DestinationSyncMode;
      cursor_field: string[];
      primary_key: string[][];
   };
}

export type SyncMode = 'full_refresh' | 'incremental';
export type DestinationSyncMode = 'append' | 'overwrite' | 'append_dedup';

interface StreamSyncConfig {
   syncMode: 'incremental' | 'full_refresh';
   destinationSyncMode: 'append' | 'append_dedup' | 'overwrite';
   cursorField?: string[];
   primaryKey?: string[][];
}

interface StreamConfig {
   sync_mode: SyncMode;
   destination_sync_mode: DestinationSyncMode;
   cursor_field: string[];
   primary_key: string[][];
}

// Function to create stream config that uses stream's default cursor/primary key
export function createStreamConfig(
   syncMode: SyncMode,
   destinationSyncMode: DestinationSyncMode,
   stream?: RawStreamMetadata,
): StreamConfig {
   return {
      sync_mode: syncMode,
      destination_sync_mode: destinationSyncMode,
      // Use stream's default cursor field if available, otherwise empty array
      cursor_field: stream?.defaultCursorField || [],
      // Use stream's default primary key if available, otherwise empty array
      primary_key: stream?.sourceDefinedPrimaryKey || [],
   };
}

export function getCustomStreamConfig(): Record<
   string,
   (stream?: RawStreamMetadata) => StreamConfig
> {
   // ONLY the streams you specifically requested
   const baseConfigs: Record<
      string,
      (stream?: RawStreamMetadata) => StreamConfig
   > = {
      activities: (stream) =>
         createStreamConfig('incremental', 'append', stream),
      ad_account: (stream) =>
         createStreamConfig('full_refresh', 'overwrite', stream),
      ad_creatives: (stream) =>
         createStreamConfig('full_refresh', 'overwrite', stream),
      ad_sets: (stream) =>
         createStreamConfig('incremental', 'append_dedup', stream),
      ads: (stream) =>
         createStreamConfig('incremental', 'append_dedup', stream),
      ads_insights: (stream) =>
         createStreamConfig('incremental', 'append_dedup', stream),
      ads_insights_action_type: (stream) =>
         createStreamConfig('incremental', 'append_dedup', stream),
      ads_insights_age_and_gender: (stream) =>
         createStreamConfig('incremental', 'append_dedup', stream),
      ads_insights_country: (stream) =>
         createStreamConfig('incremental', 'append_dedup', stream),
      ads_insights_platform_and_device: (stream) =>
         createStreamConfig('incremental', 'append_dedup', stream),
      ads_insights_region: (stream) =>
         createStreamConfig('incremental', 'append_dedup', stream),
      campaigns: (stream) =>
         createStreamConfig('incremental', 'append_dedup', stream),
      custom_audiences: (stream) =>
         createStreamConfig('full_refresh', 'overwrite', stream),
      custom_conversions: (stream) =>
         createStreamConfig('full_refresh', 'overwrite', stream),
      images: (stream) =>
         createStreamConfig('incremental', 'append_dedup', stream),
   };

   return baseConfigs;
}

// Helper function to get stream config by matching stream name patterns
export function getStreamConfig(
   streamName: string,
   stream?: RawStreamMetadata,
): StreamConfig | null {
   const baseConfigs = getCustomStreamConfig();

   // Direct match - raw streams from API come without prefix
   if (baseConfigs[streamName]) {
      return baseConfigs[streamName](stream);
   }

   // Fallback: Try to match by removing account prefix (for backward compatibility)
   // This handles cases where stream names might have prefixes in some scenarios
   /*const streamNameWithoutPrefix = streamName.replace(/^[a-zA-Z0-9]+_/, '');
   if (baseConfigs[streamNameWithoutPrefix]) {
      return baseConfigs[streamNameWithoutPrefix](stream);
   }
*/
   return null;
}
