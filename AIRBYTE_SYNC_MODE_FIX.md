# Airbyte Sync Mode Configuration Fix

## Problem
The Airbyte connection was not applying the correct sync modes for Meta Ads streams. All streams were defaulting to "Full refresh | Overwrite" instead of the specific sync modes required for each stream type.

## Root Cause
The issue was in the stream name matching logic. Airbyte stream names include an account ID prefix (e.g., `a01029_activities`), but the configuration was only matching exact stream names without the prefix.

## Solution
Updated the `airbyte-helper.ts` file to:

1. **Enhanced Stream Configuration**: Added comprehensive configurations for all Meta Ads streams including the additional insight streams.

2. **Smart Stream Name Matching**: Created a new `getStreamConfig()` function that:
   - First tries exact stream name matching
   - Falls back to matching without the account prefix (e.g., `a01029_activities` → `activities`)

3. **Updated AirbyteService**: Modified the connection creation logic to use the new helper function.

## Stream Configurations Applied

### Incremental Append
- `activities`

### Full Refresh Overwrite  
- `ad_account`
- `ad_creatives`
- `custom_audiences`
- `custom_conversions`

### Incremental Append + Deduped
- `ad_sets`
- `ads`
- `ads_insights`
- `ads_insights_action_carousel_card`
- `ads_insights_action_conversion_device`
- `ads_insights_action_product_id`
- `ads_insights_action_reaction`
- `ads_insights_action_video_sound`
- `ads_insights_action_video_type`
- `ads_insights_action_type`
- `ads_insights_age_and_gender`
- `ads_insights_country`
- `ads_insights_delivery_device`
- `ads_insights_delivery_platform`
- `ads_insights_delivery_platform_and_device_platform`
- `ads_insights_demographics_age`
- `ads_insights_demographics_country`
- `ads_insights_demographics_dma_region`
- `ads_insights_demographics_gender`
- `ads_insights_dma`
- `ads_insights_platform_and_device`
- `ads_insights_region`
- `campaigns`
- `images`
- `videos`

## Files Modified

1. **`src/utils/airbyte-helper.ts`**:
   - Enhanced `getCustomStreamConfig()` with comprehensive stream configurations
   - Added new `getStreamConfig()` function with smart name matching
   - Added proper TypeScript types

2. **`src/services/AirbyteService.ts`**:
   - Updated import to use `getStreamConfig` instead of `getCustomStreamConfig`
   - Modified connection creation logic to use the new helper function

## How to Verify the Fix

1. **Create a new Airbyte connection** for Meta Ads
2. **Check the Schema tab** in the Airbyte dashboard
3. **Verify sync modes** match the requirements:
   - Activities: Incremental | Append
   - Ad Account: Full refresh | Overwrite
   - Ad Creatives: Full refresh | Overwrite
   - Ad Sets: Incremental | Append + Deduped
   - Ads: Incremental | Append + Deduped
   - Ads Insights: Incremental | Append + Deduped
   - All other insight streams: Incremental | Append + Deduped
   - Campaigns: Incremental | Append + Deduped
   - Custom Audiences: Full refresh | Overwrite
   - Custom Conversions: Full refresh | Overwrite
   - Images: Incremental | Append + Deduped

## Testing
A test script `test-stream-config.js` has been created to verify the configuration logic works correctly for both prefixed and non-prefixed stream names.

## Benefits
- **Correct sync modes**: Each stream now uses the appropriate sync mode for its data characteristics
- **Optimized performance**: Incremental syncs reduce data transfer and processing time
- **Data integrity**: Deduplication ensures clean data for streams that support it
- **Future-proof**: The smart matching logic will work with different account IDs

## Notes
- The fix is backward compatible and doesn't affect existing connections
- New connections will automatically use the correct sync modes
- Existing connections may need to be refreshed to pick up the new schema configurations
