import { EntityManager } from 'typeorm';
import * as types from '../../types';
import axios from 'axios';
import { Config } from '../../config/index';
import { ALL_KPIS, DAYS_IN } from '../../constants/index';
import {
   KPI_CALCULATE,
   KPI_CALCULATION_DAYWISE,
   TargetingInsightsKPIS,
} from '../../utils/performanceInsightsHelper';

export class PulseMetaAdsService {
   constructor(private entityManager: EntityManager) {}

   async fetchObjectives(payload: {
      client_id: string;
   }): Promise<types.FetchObjectivesResponse> {
      const { client_id } = payload;

      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_get_client_objectives($1)`;

      const result: types.FetchMetaObjectivesQueryResult[] =
         await this.entityManager.query(query, [client_id]);

      return {
         data: result?.[0]?.fn_get_client_objectives,
      };
   }

   async fetchTrackedCampaigns(payload: {
      client_id: string;
   }): Promise<types.FetchTrackedCampaignsResponse> {
      const { client_id } = payload;

      const trackedCampaignsQuery = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.performance_insights_user_track WHERE client_id = $1 AND tracked = true`;

      const trackedKPIs: types.TrackedCampaignsQueryResult[] =
         await this.entityManager.query(trackedCampaignsQuery, [client_id]);

      return {
         data: trackedKPIs,
      };
   }

   async fetchCampaignsWithBudgetSpend(
      payload: types.FetchCampaignsWithBudgetSpendPayload,
   ): Promise<types.FetchCampaignWithBudgetSpendResponse> {
      const { campaign_id, objective, client_id, start_date, end_date } =
         payload;

      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_campaign_with_spend_budget($1, $2, $3, $4, $5)`;

      const result: types.FetchCampaignsWithBudgetSpendQueryResult[] =
         await this.entityManager.query(query, [
            campaign_id,
            objective,
            start_date,
            end_date,
            client_id,
         ]);

      if (!result) {
         return { data: null };
      }

      const finalData: {
         [key: string]: types.CampaignWithBudgetSpend[];
      } = {};

      for (const data of result) {
         if (isNaN(Number(data?.fn_campaign_with_spend_budget?.kpi_value)))
            continue;
         const kpi = data?.fn_campaign_with_spend_budget?.kpi_name!;

         if (finalData[kpi]) {
            finalData[kpi].push(data?.fn_campaign_with_spend_budget!);
         } else finalData[kpi] = [data?.fn_campaign_with_spend_budget!];
      }

      return {
         data: finalData,
      };
   }

   async fetchCampaignsDaywise(
      payload: types.FetchCampaignDaywisePayload,
   ): Promise<types.FetchCampaignDaywiseResponse> {
      const {
         client_id,
         objective,
         kpis,
         start_date,
         end_date,
         prev_start_date,
         prev_end_date,
      } = payload;

      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_campaign_daywise_with_multi_kpi_value_get($1, $2, $3, $4, $5)`;

      const currentPeriod: types.FetchCampaignDaywiseKPIsQueryResult[] =
         await this.entityManager.query(query, [
            client_id,
            objective,
            kpis,
            start_date,
            end_date,
         ]);

      if (!currentPeriod?.[0]?.fn_campaign_daywise_with_multi_kpi_value_get) {
         return {
            data: null,
         };
      }

      const previousPeriod: types.FetchCampaignDaywiseKPIsQueryResult[] =
         await this.entityManager.query(query, [
            client_id,
            objective,
            kpis,
            prev_start_date,
            prev_end_date,
         ]);

      const kpiAggregate: types.KPIwiseAggregate = kpis.reduce(
         (acc: types.KPIwiseAggregate, kpi: string) => {
            acc[kpi] = 0;
            return acc;
         },
         {},
      );

      const result: types.FetchCampaignDaywiseResponse = {
         data: [],
      };

      currentPeriod[0].fn_campaign_daywise_with_multi_kpi_value_get.forEach(
         (campaign) => {
            const currentCampaign: types.DaywiseCampaignKPIsCalculated = {
               campaign_id: '',
               campaign_name: '',
               recent_campaign_status: 'ACTIVE',
               recent_currency: '',
               kpis: [],
               daywise_kpis: {},
               prev_kpi_val: {},
               current_kpi_val: {},
            };

            currentCampaign.campaign_id = campaign.campaign_id;
            currentCampaign.campaign_name = campaign.campaign_name;
            currentCampaign.recent_campaign_status =
               campaign.recent_campaign_status;
            currentCampaign.recent_currency = campaign.recent_currency;
            currentCampaign.kpis = [...campaign.kpis];

            const prevPeriodKpis = previousPeriod[0]
               .fn_campaign_daywise_with_multi_kpi_value_get
               ? previousPeriod[0].fn_campaign_daywise_with_multi_kpi_value_get.find(
                    (c) => c.campaign_id === campaign.campaign_id,
                 )?.kpis
               : null;

            currentCampaign.prev_kpi_val = prevPeriodKpis
               ? this.calculateCampaignKPI(prevPeriodKpis, { ...kpiAggregate })
               : null;

            currentCampaign.current_kpi_val = this.calculateCampaignKPI(
               campaign.kpis,
               {
                  ...kpiAggregate,
               },
            );

            currentCampaign.daywise_kpis = this.calculateDaywise(campaign.kpis);

            result?.data?.push(currentCampaign);
         },
      );

      return result;
   }

   async fetchAdsetsDaywise(
      payload: types.FetchAdsetDaywisePayload,
   ): Promise<types.FetchAdsetsDaywiseKPIsResponse> {
      const {
         client_id,
         campaign_id,
         objective,
         start_date,
         end_date,
         prev_start_date,
         prev_end_date,
      } = payload;

      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_adset_daywise_with_multi_kpi_value_get($1, $2, $3, $4, $5)`;

      const currentPeriod: types.FetchAdsetsDaywiseKPIsQueryResult[] =
         await this.entityManager.query(query, [
            client_id,
            campaign_id,
            objective,
            start_date,
            end_date,
         ]);

      if (!currentPeriod?.[0]?.fn_adset_daywise_with_multi_kpi_value_get) {
         return {
            data: null,
         };
      }

      const previousPeriod: types.FetchAdsetsDaywiseKPIsQueryResult[] =
         await this.entityManager.query(query, [
            client_id,
            campaign_id,
            objective,
            prev_start_date,
            prev_end_date,
         ]);

      const adsetsMap: Record<string, types.DaywiseAdsetKPIsCalculated> = {};

      currentPeriod?.[0]?.fn_adset_daywise_with_multi_kpi_value_get.forEach(
         (adset) => {
            const adsetId = adset.adset_id.toString();

            const convertSetStringToCSV = (input: string): string => {
               return input
                  .replace(/^{|}$/g, '')
                  .split(/","|",\s*"/)
                  .map((item) => item.replace(/^"|"$/g, ''))
                  .join(', ');
            };

            if (!adsetsMap[adsetId]) {
               adsetsMap[adsetId] = {
                  adset_id: adsetId,
                  adset_name: adset.adset_name,
                  adset_status: adset.latest_adset_status,
                  audience_behavior: adset.latest_audience_behavior
                     ? convertSetStringToCSV(adset.latest_audience_behavior)
                     : '',
                  audience_interest: adset.latest_audience_interest
                     ? convertSetStringToCSV(adset.latest_audience_interest)
                     : '',
                  performance_category: {
                     category: '',
                     insights: '',
                  },
                  kpis: [],
               };
            }
         },
      );

      const adsets = Object.values(adsetsMap);

      adsets.forEach((adset) => {
         const currentAdsetKPIs =
            currentPeriod?.[0]?.fn_adset_daywise_with_multi_kpi_value_get?.filter(
               (adsetKPIs) => String(adsetKPIs.adset_id) === adset.adset_id,
            );

         const prevAdsetKPIs = previousPeriod?.[0]
            ?.fn_adset_daywise_with_multi_kpi_value_get
            ? previousPeriod?.[0]?.fn_adset_daywise_with_multi_kpi_value_get?.filter(
                 (adsetKPIs) => String(adsetKPIs.adset_id) === adset.adset_id,
              )
            : null;

         const uniqueKPIs = [
            ...new Set(currentAdsetKPIs.map((item) => item.kpi_name)),
         ];

         uniqueKPIs.forEach((kpi) => {
            const adsetKPISkeleton: types.AdsetKPIs = {
               kpi_name: '',
               kpi_previous: 0,
               kpi_current: 0,
               audience_interests: '',
               audience_behaviors: '',
            };

            adsetKPISkeleton.kpi_name = kpi;
            adsetKPISkeleton.kpi_previous = prevAdsetKPIs
               ? this.calculateAdsetKPI(prevAdsetKPIs, kpi)
               : null;
            adsetKPISkeleton.kpi_current = this.calculateAdsetKPI(
               currentAdsetKPIs,
               kpi,
            );

            adset.kpis.push(adsetKPISkeleton);
         });
      });

      return {
         data: adsets,
      };
   }

   async fetchAdsDaywise(
      payload: types.FetchAdsDaywisePayload,
   ): Promise<types.FetchAdsDaywiseKPIsResponse> {
      const {
         client_id,
         campaign_id,
         objective,
         adset_id,
         start_date,
         end_date,
         prev_start_date,
         prev_end_date,
      } = payload;

      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_ad_daywise_with_multi_kpi_value_get($1, $2, $3, $4, $5, $6)`;

      const currentPeriod: types.FetchAdDaywiseKPIsQueryResult[] =
         await this.entityManager.query(query, [
            client_id,
            campaign_id,
            objective,
            adset_id,
            start_date,
            end_date,
         ]);

      if (!currentPeriod?.[0]?.fn_ad_daywise_with_multi_kpi_value_get) {
         return {
            data: null,
         };
      }

      const previousPeriod: types.FetchAdDaywiseKPIsQueryResult[] =
         await this.entityManager.query(query, [
            client_id,
            campaign_id,
            objective,
            adset_id,
            prev_start_date,
            prev_end_date,
         ]);

      const adList =
         currentPeriod?.[0]?.fn_ad_daywise_with_multi_kpi_value_get || [];

      const adsMap: Record<string, types.DaywiseAdKPIsCalculated> = {};

      await Promise.all(
         adList.map(async (ad) => {
            const adId = ad.ad_id.toString();

            if (!adsMap[adId]) {
               const adMetaDataQuery = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.m_ad_creative_table WHERE ad_id = $1`;
               const adMetaData = await this.entityManager.query(
                  adMetaDataQuery,
                  [adId],
               );

               adsMap[adId] = {
                  ad_id: adId,
                  ad_name: ad.ad_name,
                  ad_status: ad.ad_status,
                  kpis: [],
                  meta_data: adMetaData[0],
               };
            }
         }),
      );

      const ads = Object.values(adsMap);

      ads.forEach((ad) => {
         const currentAdKPIs =
            currentPeriod?.[0]?.fn_ad_daywise_with_multi_kpi_value_get?.filter(
               (adKPIs) => String(adKPIs.ad_id) === ad.ad_id,
            );

         const prevAdKPIs = previousPeriod?.[0]
            ?.fn_ad_daywise_with_multi_kpi_value_get
            ? previousPeriod?.[0]?.fn_ad_daywise_with_multi_kpi_value_get?.filter(
                 (adKPIs) => String(adKPIs.ad_id) === ad.ad_id,
              )
            : null;

         const uniqueKPIs = [
            ...new Set(currentAdKPIs.map((item) => item.kpi_name)),
         ];

         uniqueKPIs.forEach((kpi) => {
            const adKPISkeleton: types.AdKPIs = {
               kpi_name: '',
               kpi_previous: 0,
               kpi_current: 0,
            };

            adKPISkeleton.kpi_name = kpi;
            adKPISkeleton.kpi_previous = prevAdKPIs
               ? this.calculateAdKPI(prevAdKPIs, kpi)
               : null;
            adKPISkeleton.kpi_current = this.calculateAdKPI(currentAdKPIs, kpi);

            ad.kpis.push(adKPISkeleton);
         });
      });

      return {
         data: ads,
      };
   }

   async fetchTargetingDaywise(
      payload: types.FetchTargetingDaywisePayload,
   ): Promise<types.FetchTargetingDaywiseKPIsResponse> {
      const {
         client_id,
         campaign_id,
         adset_id,
         objective,
         start_date,
         end_date,
      } = payload;

      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_targeting_daywise_with_multi_kpi_value_get($1, $2, $3, $4, $5)`;

      const currentPeriod: types.FetchTargetingDaywiseKPIsQueryResult[] =
         await this.entityManager.query(query, [
            client_id,
            campaign_id,
            adset_id,
            start_date,
            end_date,
         ]);

      if (!currentPeriod?.[0]?.fn_targeting_daywise_with_multi_kpi_value_get) {
         return {
            data: null,
         };
      }

      const targetingsMap: Record<
         string,
         types.DaywiseTargetingKPIsCalculated
      > = {};

      currentPeriod?.[0]?.fn_targeting_daywise_with_multi_kpi_value_get.forEach(
         (target) => {
            const targetingKey = target.targeting_key.toString();

            if (!targetingsMap[targetingKey]) {
               targetingsMap[targetingKey] = {
                  adset_id: adset_id,
                  targeting_key: target.targeting_key,
                  targeting_type: target.targeting_type,
                  kpis: [],
               };
            }
         },
      );

      const targets = Object.values(targetingsMap);
      const allowedTargetingKeys = Object.keys(TargetingInsightsKPIS);

      targets.forEach((target) => {
         if (!allowedTargetingKeys.includes(target.targeting_type)) {
            return;
         }
         const currentTargetKPIs =
         currentPeriod?.[0]?.fn_targeting_daywise_with_multi_kpi_value_get?.filter(
            (targetKPIs) =>
               String(targetKPIs.targeting_key) === target.targeting_key,
         );

         const uniqueKPIs = [
            ...((
               TargetingInsightsKPIS[
                  target?.targeting_type as keyof typeof TargetingInsightsKPIS
               ] as { [key: string]: string[] }
            )[objective] || ['spend', 'impressions', 'ctr', 'purchase']),
         ];

         uniqueKPIs.forEach((kpi) => {
            const targetKPISkeleton: types.TargetingKPIs = {
               kpi_name: '',
               kpi_value: 0,
            };

            targetKPISkeleton.kpi_name = kpi;
            targetKPISkeleton.kpi_value = this.calculateTargetingKPI(
               currentTargetKPIs,
               kpi,
            );

            target.kpis.push(targetKPISkeleton);
         });
      });

      const targetingTypesMap: Record<
         types.TargetingType,
         types.DaywiseTargetingKPIsCalculated[]
      > = {
         age: [],
         gender: [],
         placement: [],
         region: [],
         country: [],
      };

      targets.forEach((target) => {
         const targetingType =
            target.targeting_type.toString() as types.TargetingType;

         if (!targetingTypesMap[targetingType]) {
            targetingTypesMap[targetingType] =
               [] as types.DaywiseTargetingKPIsCalculated[];
            targetingTypesMap[targetingType].push(target);
         } else {
            targetingTypesMap[targetingType].push(target);
         }
      });

      return {
         data: targetingTypesMap,
      };
   }

   async callCasGPT<T>(
      endpoint: string,
      payload:
         | types.FetchChartInsightsPayload
         | types.FetchBenchmarkInsightsPayload
         | types.FetchAdsetInsightsPayload
         | types.FetchTargetingInsightsPayload,
   ): Promise<T | null> {
      try {
         const response = await axios.post<T>(
            `${Config.GPT_URL}/${endpoint}`,
            payload,
            {
               headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${Config.GPT_BEARER}`,
               },
            },
         );

         if (!response) {
            throw new Error('No Data found');
         }

         return response.data;
      } catch (error) {
         return null;
      }
   }

   async fetchChartInsights(payload: types.FetchChartInsightsPayload) {
      return await this.callCasGPT<Response | null>('chart-insights', payload);
   }

   async fetchBenchmarkInsights(payload: types.FetchBenchmarkInsightsPayload) {
      return await this.callCasGPT<Response | null>(
         'benchmark-insights',
         payload,
      );
   }

   async fetchAdsetInsights(payload: types.FetchAdsetInsightsPayload) {
      return await this.callCasGPT<Response | null>('adset-insights', payload);
   }

   async fetchTargetingInsights(payload: types.FetchTargetingInsightsPayload) {
      return await this.callCasGPT<Response | null>(
         'targeting-insights',
         payload,
      );
   }

   private calculateDaywise(campaignKPIs: types.KPIData[]): types.daywiseKPIs {
      if (!campaignKPIs?.length) return {};

      const validKPIs = campaignKPIs.filter((kpi) => kpi.kpi_value !== 'NaN');

      const aggreg = new Map<string, Record<string, number>>();

      for (const { kpi_name, kpi_date, kpi_value } of validKPIs) {
         if (!aggreg.has(kpi_name)) {
            aggreg.set(kpi_name, {});
         }
         aggreg.get(kpi_name)![kpi_date] =
            (aggreg.get(kpi_name)![kpi_date] || 0) + Number(kpi_value);
      }

      if (aggreg.has('roas') && aggreg.has('spend')) {
         const roasMap = aggreg.get('roas')!;
         const spendMap = aggreg.get('spend')!;
         for (const date in roasMap) {
            if (spendMap[date]) {
               roasMap[date] = Number(roasMap[date]) * Number(spendMap[date]);
            }
         }
      }

      const daywiseAgg = new Map<string, Record<string, number>>();

      aggreg.forEach((dateData, kpi) => {
         for (const [date, value] of Object.entries(dateData)) {
            const dayName = new Date(date).toLocaleString('en-IN', {
               weekday: 'long',
            });

            if (!daywiseAgg.has(kpi)) {
               daywiseAgg.set(kpi, {});
            }
            daywiseAgg.get(kpi)![dayName] =
               (daywiseAgg.get(kpi)![dayName] || 0) + value;
         }
      });

      daywiseAgg.forEach((kpiValues, kpiName) => {
         if (kpiName in KPI_CALCULATION_DAYWISE) {
            for (const day in kpiValues) {
               kpiValues[day] = KPI_CALCULATION_DAYWISE[
                  kpiName as keyof typeof KPI_CALCULATION_DAYWISE
               ](day, daywiseAgg);
            }
         }
      });

      return Object.fromEntries(daywiseAgg);
   }

   private splitCampaignKPIByDate(
      data: types.CampaignKPIs[],
   ): types.DateWiseAgg {
      const dateAgg: types.DateWiseAgg = {};

      data.forEach((x) => {
         const dateKey: string = x.kpi_date.split('T')[0];
         if (dateAgg[dateKey]) {
            dateAgg[dateKey] = {
               ...dateAgg[dateKey],
               [x.kpi_name]: x.kpi_value,
            };
         } else {
            dateAgg[dateKey] = { [x.kpi_name]: x.kpi_value };
         }
      });
      return dateAgg;
   }

   private calculateSum(data: types.DateAgg, kpi: string): number {
      let sum = 0;

      for (const date in data) {
         const value = data[date][kpi];
         if (value) sum += Number(value);
      }

      return sum;
   }

   private calculateCampaignKPI(
      campaignKPIs: types.CampaignKPIs[],
      kpiAggregate: types.KPIwiseAggregate,
   ): types.KPIAggregate {
      const validKPIs = campaignKPIs.filter((kpi) => kpi.kpi_value !== 'NaN');

      const dateAgg = this.splitCampaignKPIByDate(validKPIs);

      for (const kpi in kpiAggregate) {
         const kpiKey: string = kpi;
         if (KPI_CALCULATE[kpiKey as types.KPIKeys]) {
            kpiAggregate[kpi] = KPI_CALCULATE[kpiKey as types.KPIKeys](dateAgg);
         } else {
            kpiAggregate[kpi] = this.calculateSum(dateAgg, kpi);
         }
      }

      return kpiAggregate;
   }

   private splitAdsetKPIByDate(
      data: types.DaywiseAdsetKPIs[],
   ): types.DateWiseAgg {
      const dateAgg: types.DateWiseAgg = {};

      data.forEach((x) => {
         const dateKey: string = x.date_time.split('T')[0];
         if (dateAgg[dateKey]) {
            dateAgg[dateKey] = {
               ...dateAgg[dateKey],
               [x.kpi_name]: x.kpi_value,
            };
         } else {
            dateAgg[dateKey] = { [x.kpi_name]: x.kpi_value };
         }
      });
      return dateAgg;
   }

   private calculateAdsetKPI(
      adsetKPIs: types.DaywiseAdsetKPIs[],
      kpi: string,
   ): number {
      const validKPIs = adsetKPIs.filter((a) => a.kpi_value !== 'NaN');
      const dateAgg = this.splitAdsetKPIByDate(validKPIs);

      if (KPI_CALCULATE[kpi as types.KPIKeys]) {
         return KPI_CALCULATE[kpi as types.KPIKeys](dateAgg);
      } else {
         return this.calculateSum(dateAgg, kpi);
      }
   }

   private splitAdKPIByDate(data: types.DaywiseAdKPIs[]): types.DateWiseAgg {
      const dateAgg: types.DateWiseAgg = {};

      data.forEach((x) => {
         const dateKey: string = x.date_time.split('T')[0];
         if (dateAgg[dateKey]) {
            dateAgg[dateKey] = {
               ...dateAgg[dateKey],
               [x.kpi_name]: x.kpi_value,
            };
         } else {
            dateAgg[dateKey] = { [x.kpi_name]: x.kpi_value };
         }
      });
      return dateAgg;
   }

   private calculateAdKPI(adKPIs: types.DaywiseAdKPIs[], kpi: string): number {
      const validKPIs = adKPIs.filter((a) => a.kpi_value !== 'NaN');
      const dateAgg = this.splitAdKPIByDate(validKPIs);

      if (KPI_CALCULATE[kpi as types.KPIKeys]) {
         return KPI_CALCULATE[kpi as types.KPIKeys](dateAgg);
      } else {
         return this.calculateSum(dateAgg, kpi);
      }
   }

   private splitTargetingKPIByDate(
      data: types.DaywiseTargetingKPIs[],
   ): types.DateWiseAgg {
      const dateAgg: types.DateWiseAgg = {};

      data.forEach((x) => {
         const dateKey: string = x.date.split('T')[0];
         if (dateAgg[dateKey]) {
            dateAgg[dateKey] = {
               ...dateAgg[dateKey],
               [x.kpi_name]: x.kpi_value,
            };
         } else {
            dateAgg[dateKey] = { [x.kpi_name]: x.kpi_value };
         }
      });
      return dateAgg;
   }

   private calculateTargetingKPI(
      targetKPIs: types.DaywiseTargetingKPIs[],
      kpi: string,
   ): number {
      const validKPIs = targetKPIs.filter((a) => a.kpi_value !== 'NaN');
      const dateAgg = this.splitTargetingKPIByDate(validKPIs);

      if (KPI_CALCULATE[kpi as types.KPIKeys]) {
         return KPI_CALCULATE[kpi as types.KPIKeys](dateAgg);
      } else {
         return this.calculateSum(dateAgg, kpi);
      }
   }

   private splitTrackedCampaignKPIByDate(
      data: types.KPIData[],
   ): types.DateWiseAgg {
      const dateAgg: types.DateWiseAgg = {};

      data.forEach((x) => {
         const dateKey: string = x.kpi_date.split('T')[0];
         if (dateAgg[dateKey]) {
            dateAgg[dateKey] = {
               ...dateAgg[dateKey],
               [x.kpi_name]: x.kpi_value,
            };
         } else {
            dateAgg[dateKey] = { [x.kpi_name]: x.kpi_value };
         }
      });
      return dateAgg;
   }

   async updateUserTrack(
      client_id: string,
      objective: string,
      kpi_name: string,
      campaign_id: string,
      tracked: boolean,
      channel: string,
   ): Promise<string | null> {
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_update_performance_insights_user_track($1, $2, $3, $4, $5,$6)`;
      const result: [string] = await this.entityManager.query(query, [
         client_id,
         objective,
         kpi_name,
         campaign_id,
         tracked,
         channel,
      ]);

      return result[0] || null;
   }

   async fetchTrackedCampaignKPIs(
      payload: types.FetchTrackedCampaignKPIsPayload,
   ): Promise<{
      currentPeriod: types.CampaignData[];
      prevPeriod: types.CampaignData[];
   } | null> {
      const {
         client_id,
         channel,
         groupBy,
         start_date,
         end_date,
         prev_start_date,
         prev_end_date,
      } = payload;

      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_get_performance_insights_tracked_kpis_new($1, $2, $3, $4)`;

      const currentPeriod: types.TrackedKpisResponse =
         await this.entityManager.query(query, [
            client_id,
            channel,
            start_date,
            end_date,
         ]);

      const prevPeriod: types.TrackedKpisResponse =
         await this.entityManager.query(query, [
            client_id,
            channel,
            prev_start_date,
            prev_end_date,
         ]);

      const currentPeriodData: types.CampaignData[] = currentPeriod.flatMap(
         (item) => item.fn_get_performance_insights_tracked_kpis_new,
      );

      const finalCurrentPeriod = this.groupKpis(
         currentPeriodData,
         groupBy,
         start_date,
         end_date,
      );

      const prevPeriodData: types.CampaignData[] = prevPeriod.flatMap(
         (item) => item.fn_get_performance_insights_tracked_kpis_new,
      );

      const finalPrevPeriod = this.groupKpis(
         prevPeriodData,
         groupBy,
         start_date,
         end_date,
      );

      return { currentPeriod: finalCurrentPeriod, prevPeriod: finalPrevPeriod };
   }

   private groupKpis(
      campaignData: types.CampaignData[],
      groupBy: string,
      startDate: string,
      endDate: string,
   ): types.CampaignData[] {
      const groupedCampaigns = campaignData.map((campaign) => {
         if (!campaign.kpis) {
            campaign.total_val = null;
            campaign.grouped_kpis = null;
         }

         const validKPIs = campaign.kpis?.filter(
            (kpi) => kpi.kpi_value !== 'NaN',
         );

         const dateAgg = this.splitTrackedCampaignKPIByDate(validKPIs || []);
         const kpiAggregate: types.KPIAggregate = { ...ALL_KPIS };

         for (const kpi in kpiAggregate) {
            const kpiKey: string = kpi;
            if (KPI_CALCULATE[kpiKey as types.KPIKeys]) {
               kpiAggregate[kpi] =
                  KPI_CALCULATE[kpiKey as types.KPIKeys](dateAgg);
            } else {
               kpiAggregate[kpi] = this.calculateSum(dateAgg, kpi);
            }
         }
         campaign.total_val = { ...kpiAggregate };

         if (groupBy !== 'day') {
            const groupedKPIs = this.splitKPIByGroupBy(
               validKPIs || [],
               groupBy,
               startDate,
               endDate,
            );
            campaign.grouped_kpis = groupedKPIs;
         }

         return campaign;
      });

      return groupedCampaigns;
   }

   private splitKPIByGroupBy(
      data: types.KPIData[],
      groupBy: string,
      startDate: string,
      endDate: string,
   ) {
      const result: types.GroupedKPIAggregate = {};

      const filteredKPIbyRange = this.filterByRange(
         startDate,
         endDate,
         data,
         groupBy,
      );

      for (const [key, kpis] of Object.entries(filteredKPIbyRange)) {
         const kpiAggregate: types.KPIAggregate = { ...ALL_KPIS };
         const dateAgg = this.splitTrackedCampaignKPIByDate(kpis || []);
         for (const kpi in kpiAggregate) {
            const kpiKey: string = kpi;
            if (KPI_CALCULATE[kpiKey as types.KPIKeys]) {
               kpiAggregate[kpi] =
                  KPI_CALCULATE[kpiKey as types.KPIKeys](dateAgg);
            } else {
               kpiAggregate[kpi] = this.calculateSum(dateAgg, kpi);
            }
         }
         result[key] = { ...kpiAggregate };
      }

      return result;
   }

   private filterByRange(
      startDateStr: string,
      endDateStr: string,
      data: types.KPIData[],
      groupBy: string,
   ) {
      const startDateValue = startDateStr.split('-');
      const endDateValue = endDateStr.split('-');
      const startDate = new Date(
         Date.UTC(
            +startDateValue[0],
            +startDateValue[1] - 1,
            +(startDateValue[2].length === 1
               ? `0${startDateValue[2]}`
               : startDateValue[2]),
         ),
      );
      const endDate = new Date(
         Date.UTC(
            +endDateValue[0],
            +endDateValue[1] - 1,
            +(endDateValue[2].length === 1
               ? `0${endDateValue[2]}`
               : endDateValue[2]),
         ),
      );

      startDate.setUTCHours(0, 0, 0, 0);
      endDate.setUTCHours(0, 0, 0, 0);
      const result: { [key: string]: types.KPIData[] } = {};
      const days = DAYS_IN[groupBy];

      const currentStart = new Date(startDate);
      let currentEnd = new Date(currentStart);
      currentEnd.setDate(currentEnd.getDate() + Number(days) - 1);
      while (currentStart <= endDate) {
         if (currentEnd > endDate) {
            currentEnd = new Date(endDate);
         }

         const weekKey = `${currentStart.toISOString().split('T')[0]} - ${currentEnd.toISOString().split('T')[0]}`;

         const filteredItems = data.filter((item) => {
            const itemDate = new Date(item.kpi_date);
            return itemDate >= currentStart && itemDate <= currentEnd;
         });

         result[weekKey] = filteredItems;

         currentStart.setDate(currentStart.getDate() + days);
         currentEnd = new Date(currentStart);
         currentEnd.setDate(currentEnd.getDate() + days - 1);
      }

      return result;
   }

   async fetchAggregatePrevKpis(
      client_id: string,
      channel: string,
      start_date: string,
      end_date: string,
      prev_start_date: string,
      prev_end_date: string,
   ): Promise<{ result: types.CampaignData[] } | null> {
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_get_performance_insights_tracked_kpis_new($1, $2, $3, $4)`;
      const result: types.TrackedKpisResponse = await this.entityManager.query(
         query,
         [client_id, channel, prev_start_date, prev_end_date],
      );

      const fnResult: types.CampaignData[] = result
         .flatMap((item) => item.fn_get_performance_insights_tracked_kpis_new)
         .map((item) => ({
            ...item,
            kpi_start_date: start_date,
            kpi_end_date: end_date,
            kpi_prev_start_date: prev_start_date,
            kpi_prev_end_date: prev_end_date,
         }));

      const groupedResult = this.groupKpis(
         fnResult,
         'day',
         prev_start_date,
         prev_end_date,
      );

      return { result: groupedResult };
   }
}
