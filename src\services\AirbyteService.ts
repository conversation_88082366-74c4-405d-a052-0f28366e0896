/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
   getFrontendRedirectUrl,
   // getLastYearDate,
   getLastMonthDate,
   getStartDate,
} from '../utils/common';
import { Config } from '../config';
import {
   makeDeleteRequest,
   makeGetRequest,
   makePostRequest,
} from '../config/agent';
import {
   AirbyteCatalog,
   AirbyteStream,
   ConfiguredAirbyteCatalog,
   ConfiguredAirbyteStream,
   getStreamConfig,
   RawStreamMetadata,
} from '../utils/airbyte-helper';

enum SourceType {
   FACEBOOK = 'facebook-marketing',
   GOOGLE_ADS = 'google-ads',
}

function getPayloadForSource(
   sourceType: string,
   name: string,
   workspaceId: string,
   params: Record<string, object>,
   secretId?: string,
   startDate?: string,
   endDate?: string,
) {
   const configurations: { [key: string]: any } = {
      [SourceType.FACEBOOK]: {
         configuration: {
            sourceType: SourceType.FACEBOOK,
            ...(startDate ? { start_date: startDate } : {}),
            ...(endDate ? { endDate: endDate } : {}),
            account_ids: params.accountIds,
            credentials: {
               auth_type: 'Client',
            },
         },
         name,
         workspaceId,
         secretId,
      },
      [SourceType.GOOGLE_ADS]: {
         secretId,
         configuration: {
            sourceType: SourceType.GOOGLE_ADS,
            credentials: {
               developer_token: Config.GOOGLE_ADS_DEVELOPER_TOKEN,
               // client_id: Config.GOOGLE_CLIENT_ID,
               // client_secret: Config.GOOGLE_CLIENT_SECRET,
               // refresh_token: params.refreshToken,
               // access_token: params.accessToken,
            },
         },
         name,
         workspaceId,
      },
   };

   if (configurations[sourceType]) {
      return configurations[sourceType];
   } else {
      throw new Error('Invalid sourceType');
   }
}

export class AirbyteService {
   constructor(private apiBaseEndpoint: string) {}

   async createWorkspace(name: string) {
      const url = '/workspaces';
      return makePostRequest(url, { name });
   }

   async getWorkspaceDetails(workspaceId: string) {
      const url = '/workspaces/' + workspaceId;
      const options = {
         headers: {
            accept: 'application/json',
            'content-type': 'application/json',
         },
      };

      return makeGetRequest(url, options);
   }

   async initiateAuthForSource(sourceType: string) {
      const url = '/sources/initiateOAuth';

      const payload = {
         sourceType,
         redirectUrl: getFrontendRedirectUrl(
            Config.FRONTEND_DOMAIN?.split(',')[0] || '',
            'integrations',
         ),
         workspaceId: '2170db43-b3a6-4320-bb59-7f626f60d4ff',
      };

      return makePostRequest(url, payload);
   }

   async createSource(
      name: string,
      workspaceId: string,
      sourceType: string,
      credentials: Record<string, object>,
      secretId?: string,
      dateRange?: string,
   ) {
      const url = this.apiBaseEndpoint + 'sources';

      const payload = getPayloadForSource(
         sourceType,
         name,
         workspaceId,
         credentials,
         secretId,
         dateRange ? getStartDate(dateRange) : undefined,
      ) as Record<string, object>;

      return makePostRequest<{
         sourceId: string;
      }>(url, payload);
   }

   async deleteSource(sourceId: string) {
      const url = `${this.apiBaseEndpoint}sources/${sourceId}`;

      return makeDeleteRequest(url);
   }

   async createDestination(name: string, workspaceId: string, schema: string) {
      const url = this.apiBaseEndpoint + 'destinations';
      const payload = {
         configuration: {
            destinationType: 'postgres',
            port: +(Config.DB_Postgres_PORT || ''),
            schema,
            ssl_mode: { mode: 'require' },
            tunnel_method: { tunnel_method: 'NO_TUNNEL' },
            host: Config.DB_Postgres_HOST,
            database: Config.DB_Postgres_DATABASE,
            username: Config.DB_Postgres_USER,
            password: Config.DB_Postgres_PASSWORD,
         },
         name,
         workspaceId,
      };
      return makePostRequest<{ destinationId: string }>(url, payload);
   }

   async createConnection(
      name: string,
      sourceId: string,
      destinationId: string,
      prefix: string | undefined,
   ) {
      const url = this.apiBaseEndpoint + 'connections';
      let rawStreams: RawStreamMetadata[] = [];

      const streamRes = await makeGetRequest<RawStreamMetadata[]>(
         `${this.apiBaseEndpoint}streams?sourceId=${sourceId}`,
      );
      rawStreams = streamRes;

      const syncCatalog: ConfiguredAirbyteCatalog = {
         streams: rawStreams.map((stream): ConfiguredAirbyteStream => {
            const streamName = stream.streamName;
            const customConfig = getStreamConfig(streamName, stream);

            const airbyteStream: AirbyteStream = {
               name: streamName,
               supported_sync_modes: stream.defaultCursorField?.length
                  ? ['incremental', 'full_refresh']
                  : ['full_refresh'],
               json_schema: {},
               source_defined_cursor: stream.sourceDefinedCursorField,
               default_cursor_field: stream.defaultCursorField || [],
               source_defined_primary_key: stream.sourceDefinedPrimaryKey || [],
            };

            const defaultConfig = {
               sync_mode: stream.defaultCursorField?.length
                  ? ('incremental' as const)
                  : ('full_refresh' as const),
               destination_sync_mode: stream.sourceDefinedPrimaryKey?.length
                  ? ('append_dedup' as const)
                  : ('append' as const),
               cursor_field: stream.defaultCursorField || [],
               primary_key: stream.sourceDefinedPrimaryKey || [],
            };

            return {
               stream: airbyteStream,
               config: {selected: true,
                  ...(customConfig || defaultConfig),
               },
            };
         }),
      };
      const payload = {
         schedule: {
            scheduleType: 'cron',
            cronExpression: Config.AIRBYTE_DATA_SYNC_FREQ || '0 0 * * *',
         },
         dataResidency: 'auto',
         namespaceDefinition: 'destination',
         namespaceFormat: null,
         nonBreakingSchemaUpdatesBehavior: 'ignore',
         name: name,
         sourceId: sourceId,
         destinationId: destinationId,
         ...(prefix ? { prefix } : {}),
         syncCatalog,
      };

      const res = makePostRequest<{ connectionId: string }>(url, payload);

      return res;
   }

   async triggerSync(connectionId: string) {
      const url = this.apiBaseEndpoint + 'jobs';
      const payload = {
         jobType: 'sync',
         connectionId,
      };
      return makePostRequest<{ jobId: string }>(url, payload);
   }
}
