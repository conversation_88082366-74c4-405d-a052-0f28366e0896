// Test script to verify stream configuration
const { getStreamConfig } = require('./src/utils/airbyte-helper.ts');

// Test cases based on your EXACT requirements
const testCases = [
   // With account prefix - ONLY the streams you requested
   { streamName: 'a01029_activities', expected: 'incremental + append' },
   { streamName: 'a01029_ad_account', expected: 'full_refresh + overwrite' },
   { streamName: 'a01029_ad_creatives', expected: 'full_refresh + overwrite' },
   { streamName: 'a01029_ad_sets', expected: 'incremental + append_dedup' },
   { streamName: 'a01029_ads', expected: 'incremental + append_dedup' },
   {
      streamName: 'a01029_ads_insights',
      expected: 'incremental + append_dedup',
   },
   {
      streamName: 'a01029_ads_insights_action_type',
      expected: 'incremental + append_dedup',
   },
   {
      streamName: 'a01029_ads_insights_age_and_gender',
      expected: 'incremental + append_dedup',
   },
   {
      streamName: 'a01029_ads_insights_country',
      expected: 'incremental + append_dedup',
   },
   {
      streamName: 'a01029_ads_insights_platform_and_device',
      expected: 'incremental + append_dedup',
   },
   {
      streamName: 'a01029_ads_insights_region',
      expected: 'incremental + append_dedup',
   },
   { streamName: 'a01029_campaigns', expected: 'incremental + append_dedup' },
   {
      streamName: 'a01029_custom_audiences',
      expected: 'full_refresh + overwrite',
   },
   {
      streamName: 'a01029_custom_conversions',
      expected: 'full_refresh + overwrite',
   },
   { streamName: 'a01029_images', expected: 'incremental + append_dedup' },

   // Without account prefix (should also work)
   { streamName: 'activities', expected: 'incremental + append' },
   { streamName: 'ad_account', expected: 'full_refresh + overwrite' },
   { streamName: 'ad_creatives', expected: 'full_refresh + overwrite' },
];

console.log(
   'Testing stream configurations for ONLY the streams you requested...\n',
);

testCases.forEach((testCase) => {
   const config = getStreamConfig(testCase.streamName);
   if (config) {
      const actual = `${config.sync_mode} + ${config.destination_sync_mode}`;
      const status = actual === testCase.expected ? '✅ PASS' : '❌ FAIL';
      console.log(
         `${status} ${testCase.streamName}: ${actual} (expected: ${testCase.expected})`,
      );
   } else {
      console.log(`❌ FAIL ${testCase.streamName}: No config found`);
   }
});

console.log('\n✅ FINAL Configuration - EXACTLY what you requested:');
console.log('- activities: incremental append');
console.log('- ad_account: full refresh overwrite');
console.log('- ad_creatives: full refresh overwrite');
console.log('- ad_sets: incremental append + deduped');
console.log('- ads: incremental append + deduped');
console.log('- ads_insights: incremental append + deduped');
console.log('- ads_insights_action_type: incremental append + deduped');
console.log('- ads_insights_age_and_gender: incremental append + deduped');
console.log('- ads_insights_country: incremental append + deduped');
console.log('- ads_insights_platform_and_device: incremental append + deduped');
console.log('- ads_insights_region: incremental append + deduped');
console.log('- campaigns: incremental append + deduped');
console.log('- custom_audiences: full refresh overwrite');
console.log('- custom_conversions: full refresh overwrite');
console.log('- images: incremental append + deduped');
